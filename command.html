<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Command Bar</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background: rgba(20, 20, 20, 0.98);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
            backdrop-filter: blur(40px);
            -webkit-backdrop-filter: blur(40px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.08);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            pointer-events: auto; /* Allow interactions */
        }

        .container {
            padding: 24px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .search-container {
            position: relative;
            margin-bottom: 16px;
        }

        .search-input {
            width: 100%;
            padding: 18px 24px;
            font-size: 18px;
            font-weight: 400;
            background: rgba(255, 255, 255, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 12px;
            color: #ffffff;
            outline: none;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            letter-spacing: 0.01em;
        }

        .search-input:focus {
            border-color: rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.04);
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.4);
            font-weight: 400;
        }

        .results-container {
            flex: 1;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .results-container::-webkit-scrollbar {
            display: none;
        }

        .result-item {
            padding: 16px 20px;
            margin-bottom: 4px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid transparent;
            position: relative;
        }

        .result-item:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-1px);
        }

        .result-item.selected {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.15);
            transform: translateY(-1px);
        }

        .result-title {
            font-weight: 500;
            font-size: 15px;
            margin-bottom: 4px;
            color: #ffffff;
        }

        .result-description {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.6);
            line-height: 1.4;
        }

        .empty-state {
            text-align: center;
            color: rgba(255, 255, 255, 0.4);
            margin-top: 80px;
        }

        .empty-state-icon {
            font-size: 56px;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .empty-state-text {
            font-size: 16px;
            font-weight: 400;
        }

        .close-button {
            position: absolute;
            top: 12px;
            right: 12px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .close-button:hover {
            background: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.8);
        }
    </style>
</head>
<body>
    <div class="container">
        <button class="close-button" onclick="hideWindow()">×</button>
        
        <div class="search-container">
            <input 
                type="text" 
                class="search-input" 
                placeholder="Search anything..."
                id="searchInput"
            >
        </div>

        <div class="results-container" id="resultsContainer">
            <div class="empty-state">
                <div class="empty-state-icon">⚡</div>
                <div class="empty-state-text">Start typing to search</div>
            </div>
        </div>
    </div>

    <script>
        const searchInput = document.getElementById('searchInput');
        const resultsContainer = document.getElementById('resultsContainer');
        let selectedIndex = -1;
        let results = [];

        // Sample results for demonstration
        const sampleResults = [
            { title: 'Calculator', description: 'Open Calculator app', action: 'calculator' },
            { title: 'Terminal', description: 'Open Terminal', action: 'terminal' },
            { title: 'System Preferences', description: 'Open System Preferences', action: 'preferences' },
            { title: 'Activity Monitor', description: 'View system activity', action: 'activity' },
            { title: 'Finder', description: 'Open Finder', action: 'finder' }
        ];

        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.toLowerCase().trim();
            
            if (query === '') {
                showEmptyState();
                return;
            }

            // Filter results based on query
            results = sampleResults.filter(item => 
                item.title.toLowerCase().includes(query) || 
                item.description.toLowerCase().includes(query)
            );

            displayResults(results);
            selectedIndex = results.length > 0 ? 0 : -1;
            updateSelection();
        });

        searchInput.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    if (selectedIndex < results.length - 1) {
                        selectedIndex++;
                        updateSelection();
                    }
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    if (selectedIndex > 0) {
                        selectedIndex--;
                        updateSelection();
                    }
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (selectedIndex >= 0 && results[selectedIndex]) {
                        executeAction(results[selectedIndex]);
                    }
                    break;
                case 'Escape':
                    e.preventDefault();
                    hideWindow();
                    break;
            }
        });

        function showEmptyState() {
            resultsContainer.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">⚡</div>
                    <div class="empty-state-text">Start typing to search</div>
                </div>
            `;
            results = [];
            selectedIndex = -1;
        }

        function displayResults(results) {
            if (results.length === 0) {
                resultsContainer.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">🔍</div>
                        <div class="empty-state-text">No results found</div>
                    </div>
                `;
                return;
            }

            resultsContainer.innerHTML = results.map((result, index) => `
                <div class="result-item" data-index="${index}">
                    <div class="result-title">${result.title}</div>
                    <div class="result-description">${result.description}</div>
                </div>
            `).join('');

            // Add click handlers
            document.querySelectorAll('.result-item').forEach((item, index) => {
                item.addEventListener('click', () => {
                    selectedIndex = index;
                    updateSelection();
                    executeAction(results[index]);
                });
            });
        }

        function updateSelection() {
            document.querySelectorAll('.result-item').forEach((item, index) => {
                if (index === selectedIndex) {
                    item.classList.add('selected');
                } else {
                    item.classList.remove('selected');
                }
            });
        }

        function executeAction(result) {
            console.log('Executing action:', result.action);
            // Clear search
            searchInput.value = '';
            showEmptyState();
        }

        function hideWindow() {
            // This will be handled by the main process
            console.log('Hide window requested');
        }

        // Auto-focus on load but don't steal focus from other windows
        document.addEventListener('DOMContentLoaded', () => {
            // Don't auto-focus to avoid stealing focus
        });
    </script>
</body>
</html>
