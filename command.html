<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Command Bar</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: rgba(0, 0, 0, 0.8);
            height: 100vh;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .container {
            padding: 20px;
        }

        .command-input {
            width: 100%;
            padding: 12px 16px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: #ffffff;
            outline: none;
        }

        .command-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .command-input:focus {
            border-color: rgba(255, 255, 255, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <input
            type="text"
            class="command-input"
            placeholder="Type command..."
            id="commandInput"
        >
    </div>

    <script>
        const commandInput = document.getElementById('commandInput');

        commandInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                const command = commandInput.value.trim();
                if (command) {
                    console.log('Command entered:', command);
                    // Process command here
                    commandInput.value = '';
                }
            } else if (e.key === 'Escape') {
                commandInput.value = '';
            }
        });
    </script>
</body>
</html>
