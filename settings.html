<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgentZero Settings</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo {
            font-size: 32px;
            font-weight: 600;
            color: #00d4aa;
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 18px;
            color: #666;
        }

        .section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #333;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid #eee;
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-label {
            font-weight: 500;
            color: #333;
        }

        .setting-description {
            font-size: 14px;
            color: #666;
            margin-top: 4px;
        }

        .setting-control {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .toggle {
            position: relative;
            width: 48px;
            height: 24px;
            background: #ddd;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle.active {
            background: #00d4aa;
        }

        .toggle::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .toggle.active::after {
            transform: translateX(24px);
        }

        .input-field {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            min-width: 120px;
        }

        .button {
            padding: 8px 16px;
            background: #00d4aa;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s ease;
        }

        .button:hover {
            background: #00b894;
        }

        .button.secondary {
            background: #666;
        }

        .button.secondary:hover {
            background: #555;
        }

        .shortcut-display {
            background: #f8f8f8;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            color: #666;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #00d4aa;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 24px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">AgentZero</div>
            <div class="subtitle">AI Assistant Settings</div>
        </div>

        <div class="section">
            <div class="section-title">General</div>
            
            <div class="setting-item">
                <div>
                    <div class="setting-label">Launch at startup</div>
                    <div class="setting-description">Start AgentZero when you log in</div>
                </div>
                <div class="setting-control">
                    <div class="toggle" onclick="toggleSetting(this)"></div>
                </div>
            </div>

            <div class="setting-item">
                <div>
                    <div class="setting-label">Show in menu bar</div>
                    <div class="setting-description">Display tray icon in the menu bar</div>
                </div>
                <div class="setting-control">
                    <div class="toggle active" onclick="toggleSetting(this)"></div>
                </div>
            </div>

            <div class="setting-item">
                <div>
                    <div class="setting-label">Global hotkey</div>
                    <div class="setting-description">Keyboard shortcut to open command bar</div>
                </div>
                <div class="setting-control">
                    <div class="shortcut-display">⌘ Space</div>
                    <button class="button secondary">Change</button>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Command Bar</div>
            
            <div class="setting-item">
                <div>
                    <div class="setting-label">Auto-hide after action</div>
                    <div class="setting-description">Hide command bar after executing an action</div>
                </div>
                <div class="setting-control">
                    <div class="toggle active" onclick="toggleSetting(this)"></div>
                </div>
            </div>

            <div class="setting-item">
                <div>
                    <div class="setting-label">Search delay</div>
                    <div class="setting-description">Delay before showing search results (ms)</div>
                </div>
                <div class="setting-control">
                    <input type="number" class="input-field" value="100" min="0" max="1000">
                </div>
            </div>

            <div class="setting-item">
                <div>
                    <div class="setting-label">Max results</div>
                    <div class="setting-description">Maximum number of results to show</div>
                </div>
                <div class="setting-control">
                    <input type="number" class="input-field" value="8" min="1" max="20">
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">System</div>
            
            <div class="setting-item">
                <div>
                    <div class="setting-label">Status</div>
                    <div class="setting-description">Current system status</div>
                </div>
                <div class="setting-control">
                    <div class="status-indicator">
                        <div class="status-dot"></div>
                        <span>Running</span>
                    </div>
                </div>
            </div>

            <div class="setting-item">
                <div>
                    <div class="setting-label">Version</div>
                    <div class="setting-description">Current application version</div>
                </div>
                <div class="setting-control">
                    <span>1.0.0</span>
                </div>
            </div>

            <div class="setting-item">
                <div>
                    <div class="setting-label">Reset settings</div>
                    <div class="setting-description">Reset all settings to default values</div>
                </div>
                <div class="setting-control">
                    <button class="button secondary">Reset</button>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>AgentZero v1.0.0 - AI Assistant for macOS</p>
            <p>Built with Electron</p>
        </div>
    </div>

    <script>
        function toggleSetting(element) {
            element.classList.toggle('active');
        }

        // Auto-save settings when changed
        document.querySelectorAll('.input-field').forEach(input => {
            input.addEventListener('change', () => {
                console.log('Setting changed:', input.value);
                // Here you would save the setting
            });
        });

        document.querySelectorAll('.button').forEach(button => {
            button.addEventListener('click', (e) => {
                if (button.textContent === 'Change') {
                    console.log('Change hotkey requested');
                } else if (button.textContent === 'Reset') {
                    if (confirm('Are you sure you want to reset all settings?')) {
                        console.log('Reset settings');
                    }
                }
            });
        });
    </script>
</body>
</html>
