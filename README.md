# AgentZero - Raycast-like Floating Window

A modern Electron application that creates a floating window interface similar to Raycast, accessible through a system tray icon.

## Features

- **Floating Window**: Frameless, transparent window that appears on top of other applications
- **System Tray Integration**: Click the tray icon to show/hide the window
- **Global Shortcut**: Press `Cmd+Space` (or `Ctrl+Space` on Windows/Linux) to toggle the window
- **Auto-hide**: Window automatically hides when it loses focus
- **Search Interface**: Raycast-like search interface with keyboard navigation
- **Modern UI**: Clean, modern interface with blur effects and smooth animations

## Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   npm install
   ```

## Usage

### Starting the Application

```bash
npm start
```

For development with logging:
```bash
npm run dev
```

### Controls

- **Show/Hide Window**: 
  - Click the tray icon in your system tray
  - Use the global shortcut `Cmd+Space` (macOS) or `Ctrl+Space` (Windows/Linux)
  
- **Navigation**:
  - Type to search
  - Use `↑` and `↓` arrow keys to navigate results
  - Press `Enter` to execute the selected action
  - Press `Escape` to hide the window

- **Tray Menu**:
  - Right-click the tray icon for context menu options
  - Choose "Quit" to exit the application

### Window Behavior

- The window appears centered horizontally in the top portion of your screen
- It automatically hides when you click outside of it (loses focus)
- The window stays on top of other applications when visible
- It doesn't appear in the taskbar or dock

## Customization

### Window Position and Size

Edit the `createWindow()` function in `main.js` to modify:
- Window dimensions (`windowWidth`, `windowHeight`)
- Position on screen (modify `x` and `y` calculations)

### Global Shortcut

Change the global shortcut in `main.js`:
```javascript
globalShortcut.register('CommandOrControl+Space', () => {
  toggleWindow();
});
```

### Search Results

Modify the `sampleResults` array in `index.html` to customize available actions and search results.

### Styling

The interface styling can be customized by editing the CSS in `index.html`. The design uses:
- Backdrop blur effects
- Semi-transparent backgrounds
- Modern color scheme with accent color `#00d4aa`

## Technical Details

- **Framework**: Electron
- **Node.js**: 18+ required
- **Platform Support**: macOS, Windows, Linux
- **Window Features**: Frameless, transparent, always-on-top
- **Tray Icon**: Automatically resized for optimal display

## Development

The application consists of:
- `main.js`: Main Electron process with window and tray management
- `index.html`: Renderer process with the UI and search functionality
- `menu-button.png`: Tray icon image

### Key Functions

- `createWindow()`: Creates the floating window with proper positioning
- `createTray()`: Sets up system tray icon and context menu
- `toggleWindow()`: Shows/hides the window
- `showWindow()` / `hideWindow()`: Window visibility controls

## License

This project is open source and available under the MIT License.
