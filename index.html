<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgentZero</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background: rgba(30, 30, 30, 0.95);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .container {
            padding: 20px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .logo {
            font-size: 24px;
            font-weight: 600;
            color: #00d4aa;
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 14px;
            color: #888;
            margin-bottom: 20px;
        }

        .search-container {
            position: relative;
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 16px 20px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #ffffff;
            outline: none;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            border-color: #00d4aa;
            box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.2);
        }

        .search-input::placeholder {
            color: #666;
        }

        .results-container {
            flex: 1;
            overflow-y: auto;
        }

        .result-item {
            padding: 12px 16px;
            margin-bottom: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .result-item:hover {
            background: rgba(0, 212, 170, 0.1);
            border-color: rgba(0, 212, 170, 0.3);
        }

        .result-item.selected {
            background: rgba(0, 212, 170, 0.2);
            border-color: #00d4aa;
        }

        .result-title {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .result-description {
            font-size: 12px;
            color: #888;
        }

        .shortcuts {
            margin-top: auto;
            padding-top: 16px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .shortcut {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .shortcut-key {
            background: rgba(255, 255, 255, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
        }

        .empty-state {
            text-align: center;
            color: #666;
            margin-top: 40px;
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">AgentZero</div>
            <div class="subtitle">AI Assistant</div>
        </div>

        <div class="search-container">
            <input
                type="text"
                class="search-input"
                placeholder="Search or ask anything..."
                id="searchInput"
                autofocus
            >
        </div>

        <div class="results-container" id="resultsContainer">
            <div class="empty-state">
                <div class="empty-state-icon">🤖</div>
                <div>Type to search or ask a question</div>
            </div>
        </div>

        <div class="shortcuts">
            <div class="shortcut">
                <span>Open/Close</span>
                <span class="shortcut-key">⌘ Space</span>
            </div>
            <div class="shortcut">
                <span>Quit</span>
                <span class="shortcut-key">Right-click tray → Quit</span>
            </div>
        </div>
    </div>

    <script>
        const searchInput = document.getElementById('searchInput');
        const resultsContainer = document.getElementById('resultsContainer');
        let selectedIndex = -1;
        let results = [];

        // Sample results for demonstration
        const sampleResults = [
            { title: 'Open Calculator', description: 'Launch the system calculator app', action: 'calculator' },
            { title: 'Search Web', description: 'Search the web for information', action: 'web-search' },
            { title: 'System Info', description: 'Show system information', action: 'system-info' },
            { title: 'Open Terminal', description: 'Launch terminal application', action: 'terminal' },
            { title: 'File Search', description: 'Search for files on your system', action: 'file-search' }
        ];

        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.toLowerCase();

            if (query.trim() === '') {
                showEmptyState();
                return;
            }

            // Filter results based on query
            results = sampleResults.filter(item =>
                item.title.toLowerCase().includes(query) ||
                item.description.toLowerCase().includes(query)
            );

            displayResults(results);
            selectedIndex = results.length > 0 ? 0 : -1;
            updateSelection();
        });

        searchInput.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    if (selectedIndex < results.length - 1) {
                        selectedIndex++;
                        updateSelection();
                    }
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    if (selectedIndex > 0) {
                        selectedIndex--;
                        updateSelection();
                    }
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (selectedIndex >= 0 && results[selectedIndex]) {
                        executeAction(results[selectedIndex]);
                    }
                    break;
                case 'Escape':
                    e.preventDefault();
                    // Hide window (this would be handled by the main process)
                    window.close();
                    break;
            }
        });

        function showEmptyState() {
            resultsContainer.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">🤖</div>
                    <div>Type to search or ask a question</div>
                </div>
            `;
            results = [];
            selectedIndex = -1;
        }

        function displayResults(results) {
            if (results.length === 0) {
                resultsContainer.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">🔍</div>
                        <div>No results found</div>
                    </div>
                `;
                return;
            }

            resultsContainer.innerHTML = results.map((result, index) => `
                <div class="result-item" data-index="${index}">
                    <div class="result-title">${result.title}</div>
                    <div class="result-description">${result.description}</div>
                </div>
            `).join('');

            // Add click handlers
            document.querySelectorAll('.result-item').forEach((item, index) => {
                item.addEventListener('click', () => {
                    selectedIndex = index;
                    updateSelection();
                    executeAction(results[index]);
                });
            });
        }

        function updateSelection() {
            document.querySelectorAll('.result-item').forEach((item, index) => {
                if (index === selectedIndex) {
                    item.classList.add('selected');
                } else {
                    item.classList.remove('selected');
                }
            });
        }

        function executeAction(result) {
            console.log('Executing action:', result.action);
            // Here you would implement the actual actions
            // For now, just show an alert
            alert(`Executing: ${result.title}`);

            // Clear search and hide window
            searchInput.value = '';
            showEmptyState();

            // In a real implementation, you might send a message to the main process
            // to perform the action and then hide the window
        }

        // Focus the input when window becomes visible
        window.addEventListener('focus', () => {
            searchInput.focus();
        });

        // Auto-focus on load
        document.addEventListener('DOMContentLoaded', () => {
            searchInput.focus();
        });
    </script>
</body>
</html>

