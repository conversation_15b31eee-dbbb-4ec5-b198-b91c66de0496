const { app, BrowserWindow, Tray, Menu, nativeImage, globalShortcut } = require('electron');
const path = require('path');

let mainWindow;
let tray;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    show: false // Start hidden so tray can control visibility
  });

  mainWindow.loadFile('index.html');

  mainWindow.on('closed', () => {
    mainWindow = null;
  });



  function createTray() {
    const iconPath = path.join(__dirname, 'menu-button.png');

    // Create native image and resize for tray (macOS prefers 16x16 or 22x22)
    const icon = nativeImage.createFromPath(iconPath);
    const resizedIcon = icon.resize({ width: 16, height: 16 });

    tray = new Tray(resizedIcon);
    tray.setToolTip('AgentZero App');
    tray.setContextMenu(contextMenu);

    // Handle right-click to show context menu (for consistency)
    tray.on('right-click', () => {
      tray.popUpContextMenu();
    });
  }

  // App initialization
  app.whenReady().then(() => {
    createWindow();
    createTray();

    app.on('window-all-closed', () => {
      // On macOS, keep app running even when all windows are closed
      // The tray icon will allow users to reopen the window
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

