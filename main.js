const { app, BrowserWindow, Tray, Menu, nativeImage, globalShortcut, screen } = require('electron');
const path = require('path');

let commandWindow; // Floating command bar (like Raycast search)
let settingsWindow; // Main settings window with menu bar
let tray;

function createCommandWindow() {
  // Get primary display dimensions
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;

  // Calculate window position (centered horizontally, top third vertically)
  const windowWidth = 640;
  const windowHeight = 480;
  const x = Math.round((screenWidth - windowWidth) / 2);
  const y = Math.round(screenHeight * 0.12); // Position in top 12% of screen

  commandWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    x: x,
    y: y,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true
    },
    show: false, // Start hidden so tray can control visibility
    frame: false, // Remove window frame for floating appearance
    transparent: true, // Enable transparency
    resizable: false, // Prevent resizing for consistent floating appearance
    alwaysOnTop: true, // Keep window on top like Raycast
    skipTaskbar: true, // Don't show in taskbar
    hasShadow: true, // Add shadow for floating effect
    vibrancy: 'under-window', // macOS vibrancy effect
    titleBarStyle: 'hidden', // Hide title bar
    minimizable: false, // Prevent minimize
    maximizable: false, // Prevent maximize
    fullscreenable: false, // Prevent fullscreen
    movable: false, // Prevent moving (stays centered)
    closable: false, // Prevent closing via window controls
    focusable: false, // Don't steal focus from active window
    acceptFirstMouse: false, // Don't accept first mouse click
    type: 'panel' // macOS panel type for floating behavior
  });

  commandWindow.loadFile('command.html');

  // Don't auto-hide on blur - let it float independently
  commandWindow.on('closed', () => {
    commandWindow = null;
  });

  // Prevent window from being minimized
  commandWindow.on('minimize', (event) => {
    event.preventDefault();
    commandWindow.hide();
  });
}

function createSettingsWindow() {
  // Get primary display dimensions for centering
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;

  const windowWidth = 900;
  const windowHeight = 700;
  const x = Math.round((screenWidth - windowWidth) / 2);
  const y = Math.round((screenHeight - windowHeight) / 2);

  settingsWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    x: x,
    y: y,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true
    },
    show: false, // Start hidden
    frame: true, // Keep window frame for settings
    transparent: false, // No transparency for settings
    resizable: true, // Allow resizing
    alwaysOnTop: false, // Normal window behavior
    skipTaskbar: false, // Show in taskbar
    hasShadow: true,
    titleBarStyle: 'default', // Show title bar
    minimizable: true,
    maximizable: true,
    fullscreenable: true,
    movable: true,
    closable: true,
    title: 'AgentZero Settings'
  });

  settingsWindow.loadFile('settings.html');

  settingsWindow.on('closed', () => {
    settingsWindow = null;
  });

  // Create application menu for settings window
  const template = [
    {
      label: 'AgentZero',
      submenu: [
        { label: 'About AgentZero', role: 'about' },
        { type: 'separator' },
        { label: 'Preferences...', accelerator: 'Cmd+,', click: () => showSettingsWindow() },
        { type: 'separator' },
        { label: 'Hide AgentZero', accelerator: 'Cmd+H', role: 'hide' },
        { label: 'Hide Others', accelerator: 'Cmd+Alt+H', role: 'hideothers' },
        { label: 'Show All', role: 'unhide' },
        { type: 'separator' },
        { label: 'Quit', accelerator: 'Cmd+Q', click: () => app.quit() }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { label: 'Undo', accelerator: 'Cmd+Z', role: 'undo' },
        { label: 'Redo', accelerator: 'Shift+Cmd+Z', role: 'redo' },
        { type: 'separator' },
        { label: 'Cut', accelerator: 'Cmd+X', role: 'cut' },
        { label: 'Copy', accelerator: 'Cmd+C', role: 'copy' },
        { label: 'Paste', accelerator: 'Cmd+V', role: 'paste' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { label: 'Reload', accelerator: 'Cmd+R', role: 'reload' },
        { label: 'Force Reload', accelerator: 'Cmd+Shift+R', role: 'forceReload' },
        { label: 'Toggle Developer Tools', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: 'Actual Size', accelerator: 'Cmd+0', role: 'resetZoom' },
        { label: 'Zoom In', accelerator: 'Cmd+Plus', role: 'zoomIn' },
        { label: 'Zoom Out', accelerator: 'Cmd+-', role: 'zoomOut' },
        { type: 'separator' },
        { label: 'Toggle Fullscreen', accelerator: 'Ctrl+Cmd+F', role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { label: 'Minimize', accelerator: 'Cmd+M', role: 'minimize' },
        { label: 'Close', accelerator: 'Cmd+W', role: 'close' },
        { type: 'separator' },
        { label: 'Show Command Bar', accelerator: 'Cmd+Space', click: () => toggleCommandWindow() }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

function createTray() {
  const iconPath = path.join(__dirname, 'menu-button.png');

  // Create native image and resize for tray (macOS prefers 16x16 or 22x22)
  const icon = nativeImage.createFromPath(iconPath);
  const resizedIcon = icon.resize({ width: 16, height: 16 });

  tray = new Tray(resizedIcon);
  tray.setToolTip('AgentZero - Click to open');

  // Handle left-click to toggle command window visibility
  tray.on('click', () => {
    toggleCommandWindow();
  });

  // Handle right-click to show context menu
  tray.on('right-click', () => {
    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'Show Command Bar',
        click: () => {
          showCommandWindow();
        }
      },
      {
        label: 'Show Settings',
        click: () => {
          showSettingsWindow();
        }
      },
      { type: 'separator' },
      {
        label: 'Hide Command Bar',
        click: () => {
          hideCommandWindow();
        }
      },
      { type: 'separator' },
      {
        label: 'Quit',
        click: () => {
          app.quit();
        }
      }
    ]);
    tray.popUpContextMenu(contextMenu);
  });
}

function toggleCommandWindow() {
  if (commandWindow) {
    if (commandWindow.isVisible()) {
      hideCommandWindow();
    } else {
      showCommandWindow();
    }
  }
}

function showCommandWindow() {
  if (commandWindow) {
    commandWindow.showInactive(); // Show without stealing focus
    // Don't call focus() - let the active window keep focus
  }
}

function hideCommandWindow() {
  if (commandWindow) {
    commandWindow.hide();
  }
}

function showSettingsWindow() {
  if (settingsWindow) {
    settingsWindow.show();
    settingsWindow.focus();
  }
}

function hideSettingsWindow() {
  if (settingsWindow) {
    settingsWindow.hide();
  }
}

// App initialization
app.whenReady().then(() => {
  createCommandWindow();
  createSettingsWindow();
  createTray();

  // Register global shortcut (Cmd+Space like Raycast)
  globalShortcut.register('CommandOrControl+Space', () => {
    toggleCommandWindow();
  });

  app.on('activate', () => {
    // On macOS, show settings window when dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) {
      createCommandWindow();
      createSettingsWindow();
    } else {
      showSettingsWindow();
    }
  });
});

app.on('window-all-closed', () => {
  // On macOS, keep app running even when all windows are closed
  // The tray icon will allow users to reopen the window
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('will-quit', () => {
  // Unregister all shortcuts
  globalShortcut.unregisterAll();
});

// Prevent multiple instances
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    // Someone tried to run a second instance, focus our window instead
    showWindow();
  });
}

