const { app, BrowserWindow, Tray, Menu, nativeImage, globalShortcut, screen } = require('electron');
const path = require('path');

let mainWindow;
let tray;

function createWindow() {
  // Get primary display dimensions
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;

  // Calculate window position (centered horizontally, top third vertically)
  const windowWidth = 600;
  const windowHeight = 400;
  const x = Math.round((screenWidth - windowWidth) / 2);
  const y = Math.round(screenHeight * 0.15); // Position in top 15% of screen

  mainWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    x: x,
    y: y,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    show: false, // Start hidden so tray can control visibility
    frame: false, // Remove window frame for floating appearance
    transparent: true, // Enable transparency
    resizable: false, // Prevent resizing for consistent floating appearance
    alwaysOnTop: true, // Keep window on top like Raycast
    skipTaskbar: true, // Don't show in taskbar
    hasShadow: true, // Add shadow for floating effect
    vibrancy: 'under-window', // macOS vibrancy effect
    titleBarStyle: 'hidden' // Hide title bar
  });

  mainWindow.loadFile('index.html');

  // Hide window when it loses focus (like Raycast)
  mainWindow.on('blur', () => {
    if (mainWindow && mainWindow.isVisible()) {
      mainWindow.hide();
    }
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Prevent window from being minimized
  mainWindow.on('minimize', (event) => {
    event.preventDefault();
    mainWindow.hide();
  });
}

function createTray() {
  const iconPath = path.join(__dirname, 'menu-button.png');

  // Create native image and resize for tray (macOS prefers 16x16 or 22x22)
  const icon = nativeImage.createFromPath(iconPath);
  const resizedIcon = icon.resize({ width: 16, height: 16 });

  tray = new Tray(resizedIcon);
  tray.setToolTip('AgentZero - Click to open');

  // Handle left-click to toggle window visibility
  tray.on('click', () => {
    toggleWindow();
  });

  // Handle right-click to show context menu
  tray.on('right-click', () => {
    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'Show AgentZero',
        click: () => {
          showWindow();
        }
      },
      {
        label: 'Hide AgentZero',
        click: () => {
          hideWindow();
        }
      },
      { type: 'separator' },
      {
        label: 'Quit',
        click: () => {
          app.quit();
        }
      }
    ]);
    tray.popUpContextMenu(contextMenu);
  });
}

function toggleWindow() {
  if (mainWindow) {
    if (mainWindow.isVisible()) {
      hideWindow();
    } else {
      showWindow();
    }
  }
}

function showWindow() {
  if (mainWindow) {
    mainWindow.show();
    mainWindow.focus();
  }
}

function hideWindow() {
  if (mainWindow) {
    mainWindow.hide();
  }
}

// App initialization
app.whenReady().then(() => {
  createWindow();
  createTray();

  // Register global shortcut (Cmd+Space like Raycast)
  globalShortcut.register('CommandOrControl+Space', () => {
    toggleWindow();
  });

  app.on('activate', () => {
    // On macOS, re-create window when dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    } else {
      showWindow();
    }
  });
});

app.on('window-all-closed', () => {
  // On macOS, keep app running even when all windows are closed
  // The tray icon will allow users to reopen the window
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('will-quit', () => {
  // Unregister all shortcuts
  globalShortcut.unregisterAll();
});

// Prevent multiple instances
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    // Someone tried to run a second instance, focus our window instead
    showWindow();
  });
}

